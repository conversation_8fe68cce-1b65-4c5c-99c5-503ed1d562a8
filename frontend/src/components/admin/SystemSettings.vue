<template>
  <div class="modal modal-open">
    <div class="modal-box max-w-4xl">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-bold text-primary">
          <Icon name="cog" size="md" class="mr-2" />
          System Settings
        </h3>
        <button @click="$emit('close')" class="btn btn-ghost btn-circle">
          <Icon name="x" size="md" />
        </button>
      </div>

      <!-- Settings Tabs -->
      <div class="tabs tabs-boxed mb-6">
        <a 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          class="tab"
          :class="{ 'tab-active': activeTab === tab.id }"
        >
          <Icon :name="tab.icon" size="sm" class="mr-2" />
          {{ tab.name }}
        </a>
      </div>

      <!-- General Settings -->
      <div v-if="activeTab === 'general'" class="space-y-6">
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">Application Settings</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">Application Name</label>
                <input v-model="settings.appName" type="text" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">Application Version</label>
                <input v-model="settings.appVersion" type="text" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">Default Theme</label>
                <select v-model="settings.defaultTheme" class="select select-bordered">
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="cupcake">Cupcake</option>
                  <option value="synthwave">Synthwave</option>
                </select>
              </div>
              <div class="form-control">
                <label class="label">Default Language</label>
                <select v-model="settings.defaultLanguage" class="select select-bordered">
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">Feature Toggles</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Enable Analytics</span>
                  <input v-model="settings.enableAnalytics" type="checkbox" class="toggle toggle-primary" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Enable PWA</span>
                  <input v-model="settings.enablePWA" type="checkbox" class="toggle toggle-primary" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Enable Debug Mode</span>
                  <input v-model="settings.enableDebug" type="checkbox" class="toggle toggle-warning" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Maintenance Mode</span>
                  <input v-model="settings.maintenanceMode" type="checkbox" class="toggle toggle-error" />
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Email Settings -->
      <div v-if="activeTab === 'email'" class="space-y-6">
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">SMTP Configuration</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">SMTP Host</label>
                <input v-model="settings.smtpHost" type="text" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">SMTP Port</label>
                <input v-model="settings.smtpPort" type="number" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">SMTP Username</label>
                <input v-model="settings.smtpUsername" type="text" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">SMTP Password</label>
                <input v-model="settings.smtpPassword" type="password" class="input input-bordered" />
              </div>
            </div>
            <div class="form-control mt-4">
              <label class="label cursor-pointer">
                <span class="label-text">Enable SSL/TLS</span>
                <input v-model="settings.smtpSSL" type="checkbox" class="toggle toggle-primary" />
              </label>
            </div>
            <div class="card-actions mt-4">
              <button @click="testEmail" class="btn btn-secondary">
                <Icon name="email" size="sm" class="mr-2" />
                Test Email
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- API Settings -->
      <div v-if="activeTab === 'api'" class="space-y-6">
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">API Configuration</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">API Base URL</label>
                <input v-model="settings.apiBaseUrl" type="url" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">API Version</label>
                <input v-model="settings.apiVersion" type="text" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">Rate Limit (requests/minute)</label>
                <input v-model="settings.rateLimit" type="number" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">Request Timeout (seconds)</label>
                <input v-model="settings.requestTimeout" type="number" class="input input-bordered" />
              </div>
            </div>
            <div class="form-control mt-4">
              <label class="label cursor-pointer">
                <span class="label-text">Enable API Logging</span>
                <input v-model="settings.enableApiLogging" type="checkbox" class="toggle toggle-primary" />
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Backup Settings -->
      <div v-if="activeTab === 'backup'" class="space-y-6">
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">Backup Configuration</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">Backup Frequency</label>
                <select v-model="settings.backupFrequency" class="select select-bordered">
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              <div class="form-control">
                <label class="label">Retention Period (days)</label>
                <input v-model="settings.backupRetention" type="number" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">Backup Location</label>
                <input v-model="settings.backupLocation" type="text" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">Next Backup</label>
                <input v-model="settings.nextBackup" type="datetime-local" class="input input-bordered" />
              </div>
            </div>
            <div class="card-actions mt-4">
              <button @click="runBackup" class="btn btn-primary">
                <Icon name="database" size="sm" class="mr-2" />
                Run Backup Now
              </button>
              <button @click="restoreBackup" class="btn btn-secondary">
                <Icon name="upload" size="sm" class="mr-2" />
                Restore Backup
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="modal-action">
        <button @click="saveSettings" class="btn btn-primary">
          <Icon name="save" size="sm" class="mr-2" />
          Save Settings
        </button>
        <button @click="resetSettings" class="btn btn-warning">
          <Icon name="refresh" size="sm" class="mr-2" />
          Reset to Defaults
        </button>
        <button @click="$emit('close')" class="btn">Cancel</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Icon from '@/components/common/Icon.vue'

defineEmits(['close'])

const activeTab = ref('general')

const tabs = [
  { id: 'general', name: 'General', icon: 'cog' },
  { id: 'email', name: 'Email', icon: 'email' },
  { id: 'api', name: 'API', icon: 'globe' },
  { id: 'backup', name: 'Backup', icon: 'database' },
]

const settings = ref({
  // General
  appName: 'HLenergy',
  appVersion: '1.0.0',
  defaultTheme: 'light',
  defaultLanguage: 'en',
  enableAnalytics: false,
  enablePWA: true,
  enableDebug: true,
  maintenanceMode: false,
  
  // Email
  smtpHost: 'smtp.gmail.com',
  smtpPort: 587,
  smtpUsername: '',
  smtpPassword: '',
  smtpSSL: true,
  
  // API
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001',
  apiVersion: 'v1',
  rateLimit: 100,
  requestTimeout: 30,
  enableApiLogging: true,
  
  // Backup
  backupFrequency: 'daily',
  backupRetention: 30,
  backupLocation: '/backups',
  nextBackup: new Date().toISOString().slice(0, 16),
})

const saveSettings = () => {
  console.log('Saving settings:', settings.value)
  alert('Settings saved successfully!')
}

const resetSettings = () => {
  if (confirm('Reset all settings to defaults?')) {
    console.log('Resetting settings...')
    alert('Settings reset to defaults!')
  }
}

const testEmail = () => {
  console.log('Testing email configuration...')
  alert('Test email sent! Check your inbox.')
}

const runBackup = () => {
  console.log('Running backup...')
  alert('Backup started! You will be notified when complete.')
}

const restoreBackup = () => {
  console.log('Restoring backup...')
  alert('Backup restore functionality would be implemented here')
}
</script>
