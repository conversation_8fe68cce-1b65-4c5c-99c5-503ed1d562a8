import { ref, onMounted, onUnmounted, computed, getCurrentInstance } from 'vue'
import { io, Socket } from 'socket.io-client'
import { useAuthStore } from '@/stores/auth'
import { storeToRefs } from 'pinia'

// Socket.io connection state
const socket = ref<Socket | null>(null)
const isConnected = ref(false)
const isConnecting = ref(false)
const connectionError = ref<string | null>(null)
const reconnectAttempts = ref(0)
const lastPing = ref<number | null>(null)

// Custom reconnection strategy with enhanced rate limiting
const maxReconnectAttempts = ref(3)
const reconnectCycle = ref(0) // Track how many cycles of 3 attempts we've done
const isInBackoffPeriod = ref(false)
const nextReconnectTime = ref<number | null>(null)
const reconnectTimer = ref<number | null>(null)

// Enhanced error tracking and rate limiting
let consecutiveConnectionErrors = 0
let lastConnectionAttempt = 0
const CONNECTION_ATTEMPT_COOLDOWN = 5000 // 5 seconds minimum between connection attempts
const MAX_CONSECUTIVE_ERRORS = 5
const ERROR_BACKOFF_TIME = 300000 // 5 minutes after 5 consecutive errors
const WEBSOCKET_ERROR_BACKOFF = 60000 // 1 minute backoff for websocket errors specifically

// Temporary token management
const tempToken = ref<string | null>(null)
const tempTokenExpiry = ref<number | null>(null)
const isGettingTempToken = ref(false)

// Real-time data
const onlineUsers = ref<any[]>([])
const notifications = ref<any[]>([])
const analyticsEvents = ref<any[]>([])
const typingUsers = ref<Set<string>>(new Set())

export function useSocket() {
  const authStore = useAuthStore()
  const { token, user } = storeToRefs(authStore)

  // Get effective token (user token or temp token)
  const effectiveToken = computed(() => {
    return token.value || tempToken.value
  })

  // Check if temp token is expired
  const isTempTokenExpired = computed(() => {
    if (!tempTokenExpiry.value) return true
    return Date.now() > tempTokenExpiry.value
  })

  // Generate random user name
  const generateRandomName = () => {
    const adjectives = [
      'Happy', 'Clever', 'Bright', 'Swift', 'Calm', 'Bold', 'Kind', 'Wise', 'Cool', 'Smart',
      'Quick', 'Brave', 'Gentle', 'Strong', 'Cheerful', 'Friendly', 'Creative', 'Curious', 'Energetic', 'Peaceful'
    ]
    const animals = [
      'Panda', 'Fox', 'Wolf', 'Eagle', 'Dolphin', 'Tiger', 'Lion', 'Bear', 'Owl', 'Hawk',
      'Rabbit', 'Deer', 'Whale', 'Shark', 'Falcon', 'Lynx', 'Otter', 'Seal', 'Raven', 'Swan'
    ]

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
    const animal = animals[Math.floor(Math.random() * animals.length)]
    const number = Math.floor(Math.random() * 999) + 1

    return `${adjective}${animal}${number}`
  }

  // Get temporary token from API
  const getTempToken = async () => {
    if (isGettingTempToken.value) return false

    try {
      isGettingTempToken.value = true
      console.log('🔑 Getting temporary token for Socket.io...')

      const randomName = generateRandomName()

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}/api/v1/socket/temp-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: randomName,
          email: `${randomName.toLowerCase()}@temp.local`
        })
      })

      const data = await response.json()

      if (data.success) {
        tempToken.value = data.token
        // Set expiry to 50 minutes (10 minutes before actual expiry for safety)
        tempTokenExpiry.value = Date.now() + (50 * 60 * 1000)

        console.log('✅ Temporary token obtained successfully')
        console.log('   User ID:', data.user.userId)
        console.log('   Expires in: 50 minutes (with 10min safety buffer)')

        return true
      } else {
        console.error('❌ Failed to get temporary token:', data.message)
        return false
      }
    } catch (error) {
      console.error('❌ Error getting temporary token:', error)
      return false
    } finally {
      isGettingTempToken.value = false
    }
  }

  // Refresh temp token if needed
  const refreshTempTokenIfNeeded = async () => {
    if (token.value) return true // User is authenticated, no need for temp token

    if (!tempToken.value || isTempTokenExpired.value) {
      console.log('🔄 Temp token expired or missing, getting new one...')
      return await getTempToken()
    }

    return true
  }

  // Connection management with enhanced rate limiting
  const connect = async () => {
    if (socket.value?.connected) return

    // Don't make requests when offline
    if (!navigator.onLine) {
      console.log('🌐 Offline - skipping Socket.io connection attempt')
      connectionError.value = 'Offline'
      return
    }

    // Rate limiting - prevent spamming connection attempts
    const now = Date.now()
    if (now - lastConnectionAttempt < CONNECTION_ATTEMPT_COOLDOWN) {
      console.log('⏱️ Socket.io connection attempt rate limited')
      return
    }

    // Backoff after consecutive errors
    if (consecutiveConnectionErrors >= MAX_CONSECUTIVE_ERRORS) {
      if (now - lastConnectionAttempt < ERROR_BACKOFF_TIME) {
        console.log(`🚫 Socket.io connection in backoff mode (${consecutiveConnectionErrors} consecutive errors)`)
        connectionError.value = 'In backoff mode due to repeated connection failures'
        return
      }
      // Reset error count after backoff period
      consecutiveConnectionErrors = 0
      console.log('🔄 Socket.io error backoff period ended, resetting error count')
    }

    // Ensure we have a valid token (user or temporary)
    const hasValidToken = await refreshTempTokenIfNeeded()

    if (!hasValidToken) {
      console.error('❌ Failed to obtain authentication token')
      connectionError.value = 'Failed to obtain authentication token'
      consecutiveConnectionErrors++
      return
    }

    const currentToken = effectiveToken.value
    if (!currentToken) {
      console.error('❌ No valid token available')
      connectionError.value = 'No valid token available'
      consecutiveConnectionErrors++
      return
    }

    lastConnectionAttempt = now
    isConnecting.value = true
    connectionError.value = null

    const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'

    console.log('🔌 Connecting to Socket.io server:', serverUrl)
    console.log('🔑 Using token type:', token.value ? 'User Token' : 'Temporary Token')

    try {
      socket.value = io(serverUrl, {
        auth: {
          token: currentToken
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        autoConnect: true,
        // Disable automatic reconnection - we'll handle it manually
        reconnection: false,
        forceNew: false
      })

      setupEventListeners()
    } catch (error: any) {
      console.error('❌ Failed to create Socket.io connection:', error)
      consecutiveConnectionErrors++
      connectionError.value = error.message
      isConnecting.value = false
    }
  }

  const disconnect = () => {
    if (socket.value) {
      socket.value.disconnect()
      socket.value = null
    }
    isConnected.value = false
    isConnecting.value = false
    resetReconnectionState()
  }

  const setupEventListeners = () => {
    if (!socket.value) return

    // Connection events
    socket.value.on('connect', () => {
      console.log('✅ Connected to Socket.io server')
      isConnected.value = true
      isConnecting.value = false
      connectionError.value = null
      lastPing.value = Date.now()

      // Reset error counters on successful connection
      consecutiveConnectionErrors = 0

      // Reset reconnection state on successful connection
      resetReconnectionState()
    })

    socket.value.on('disconnect', (reason) => {
      console.log('❌ Disconnected from Socket.io server:', reason)
      isConnected.value = false
      isConnecting.value = false

      // Only attempt reconnection for unexpected disconnections
      if (reason !== 'io client disconnect' && reason !== 'io server disconnect') {
        console.log('🔄 Unexpected disconnection, scheduling reconnect...')
        scheduleReconnect()
      }
    })

    socket.value.on('connect_error', async (error) => {
      console.error('🔌 Socket.io connection error:', error)
      isConnecting.value = false
      connectionError.value = error.message

      // Increment error counter
      consecutiveConnectionErrors++

      // Special handling for websocket errors
      if (error.message.includes('websocket') || error.message.includes('TransportError')) {
        console.warn(`🚫 WebSocket error detected (${consecutiveConnectionErrors}/${MAX_CONSECUTIVE_ERRORS}). Implementing backoff strategy.`)

        if (consecutiveConnectionErrors >= MAX_CONSECUTIVE_ERRORS) {
          console.warn(`🚫 Socket.io connection disabled for ${ERROR_BACKOFF_TIME / 60000} minutes due to consecutive websocket errors`)
          return // Don't schedule reconnect during backoff
        }
      }

      // If authentication error and using temp token, try to refresh
      if (error.message.includes('Authentication') && !token.value) {
        console.log('🔄 Authentication error with temp token, attempting refresh...')
        const refreshed = await getTempToken()
        if (refreshed) {
          console.log('🔄 Retrying connection with new temp token...')
          // Use longer delay for auth retries to prevent spam
          createTimeout(() => connect(), 5000)
          return // Don't schedule normal reconnect for auth errors
        }
      }

      // Schedule reconnect using our custom strategy (with rate limiting)
      scheduleReconnect()
    })

    // Note: Removed automatic reconnect event handlers since we handle reconnection manually

    // Server confirmation
    socket.value.on('connected', (data) => {
      console.log('🎉 Socket.io connection confirmed:', data)
    })

    // Real-time notifications
    socket.value.on('notification:received', (notification) => {
      notifications.value.unshift(notification)
      showNotification(notification)
    })

    socket.value.on('notification:urgent', (notification) => {
      notifications.value.unshift(notification)
      showUrgentNotification(notification)
    })

    // Analytics events
    socket.value.on('analytics:realtime_pageview', (event) => {
      analyticsEvents.value.unshift(event)
    })

    socket.value.on('analytics:realtime_click', (event) => {
      analyticsEvents.value.unshift(event)
    })

    socket.value.on('analytics:visitor_count', (data) => {
      onlineUsers.value = data.users
    })

    // Communication events
    socket.value.on('communication:message_received', (message) => {
      handleNewMessage(message)
    })

    socket.value.on('communication:auto_response', (response) => {
      handleAutoResponse(response)
    })

    // Typing indicators
    socket.value.on('typing:user_started', (data) => {
      typingUsers.value.add(data.userId)
    })

    socket.value.on('typing:user_stopped', (data) => {
      typingUsers.value.delete(data.userId)
    })

    // Project updates
    socket.value.on('project:status_updated', (update) => {
      handleProjectUpdate(update)
    })

    socket.value.on('project:milestone_completed', (milestone) => {
      handleMilestone(milestone)
    })

    // Energy monitoring
    socket.value.on('energy:realtime_data', (data) => {
      handleEnergyData(data)
    })

    socket.value.on('notification:energy_alert', (alert) => {
      handleEnergyAlert(alert)
    })

    // System events
    socket.value.on('system:maintenance_notice', (notice) => {
      handleMaintenanceNotice(notice)
    })

    socket.value.on('test:message', (data) => {
      console.log('🧪 Test message received:', data)
    })

    // User presence
    socket.value.on('user:online', (data) => {
      console.log('👤 User came online:', data.userData.email || data.userData.name)
      // Update online users list
      if (!onlineUsers.value.find(u => u.userId === data.userId)) {
        onlineUsers.value.push(data)
      }
    })

    socket.value.on('user:offline', (data) => {
      console.log('👤 User went offline:', data.userData.email || data.userData.name)
      // Remove from online users list
      onlineUsers.value = onlineUsers.value.filter(u => u.userId !== data.userId)
    })

    // Listen for complete users list
    socket.value.on('users:list', (users) => {
      console.log('👥 Received users list:', users.length, 'users')
      onlineUsers.value = users || []
    })

    // Listen for users list updates
    socket.value.on('users:update', (data) => {
      console.log('👥 Users list updated:', data.count, 'users online')
      onlineUsers.value = data.users || []
    })

    // Ping/pong for connection health
    socket.value.on('pong', () => {
      lastPing.value = Date.now()
    })

    // Additional Socket.io events for comprehensive logging
    socket.value.on('broadcast:message', (data) => {
      console.log('📡 Broadcast message received:', data)
      console.table(data)
    })

    socket.value.on('analytics:api_event', (data) => {
      console.log('📊 Analytics API event:', data)
      console.table(data)
    })

    socket.value.on('room:joined', (data) => {
      console.log('🏠 Joined room:', data)
      console.table(data)
    })

    socket.value.on('room:left', (data) => {
      console.log('🏠 Left room:', data)
      console.table(data)
    })

    socket.value.on('room:user_joined', (data) => {
      console.log('🏠 User joined room:', data)
    })

    socket.value.on('room:user_left', (data) => {
      console.log('🏠 User left room:', data)
    })

    socket.value.on('system:announcement', (data) => {
      console.log('📢 System announcement:', data)
      console.table(data)
    })

    socket.value.on('error', (error) => {
      console.error('❌ Socket.io error:', error)

      // Track general socket errors
      consecutiveConnectionErrors++

      // Special handling for transport errors
      if (error.message && (error.message.includes('websocket') || error.message.includes('Transport'))) {
        console.warn(`🚫 Transport error detected (${consecutiveConnectionErrors}/${MAX_CONSECUTIVE_ERRORS})`)

        if (consecutiveConnectionErrors >= MAX_CONSECUTIVE_ERRORS) {
          console.warn('🚫 Too many transport errors, entering backoff mode')
          disconnect() // Disconnect to prevent further spam
        }
      }
    })

    // Debug: Listen for any event
    socket.value.onAny((event, ...args) => {
      console.log('📥 Socket.io event received:', event, args)
    })

    // Debug: Listen for any outgoing event
    socket.value.onAnyOutgoing((event, ...args) => {
      console.log('📤 Socket.io event sent:', event, args)
    })
  }

  // Event emitters
  const trackPageView = (page: string, title: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('analytics:pageview', {
      page,
      title,
      timestamp: new Date().toISOString()
    })
  }

  const trackClick = (element: string, page: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('analytics:click', {
      element,
      page,
      timestamp: new Date().toISOString()
    })
  }

  const trackFormSubmit = (form: string, success: boolean) => {
    if (!socket.value?.connected) return

    socket.value.emit('analytics:form_submit', {
      form,
      success,
      timestamp: new Date().toISOString()
    })
  }

  const sendMessage = (conversationId: string, message: string, attachments?: any[]) => {
    if (!socket.value?.connected) return

    socket.value.emit('communication:chat_message', {
      conversationId,
      message,
      attachments: attachments || [],
      timestamp: new Date().toISOString()
    })
  }

  const startTyping = (roomId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('typing:start', { roomId })
  }

  const stopTyping = (roomId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('typing:stop', { roomId })
  }

  const joinRoom = (roomId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('room:join', roomId)
  }

  const leaveRoom = (roomId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('room:leave', roomId)
  }

  const markNotificationAsRead = (notificationId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('notification:read', { notificationId })
  }

  // Event handlers
  const showNotification = (notification: any) => {
    // Integrate with your notification system
    console.log('📢 New notification:', notification)
    
    // You can integrate with a toast library here
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico'
      })
    }
  }

  const showUrgentNotification = (notification: any) => {
    console.log('🚨 Urgent notification:', notification)
    
    // Show more prominent notification for urgent messages
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`🚨 ${notification.title}`, {
        body: notification.message,
        icon: '/favicon.ico',
        requireInteraction: true
      })
    }
  }

  const handleNewMessage = (message: any) => {
    console.log('💬 New message:', message)
    // Handle new chat message
  }

  const handleAutoResponse = (response: any) => {
    console.log('🤖 Auto response:', response)
    // Handle auto response
  }

  const handleProjectUpdate = (update: any) => {
    console.log('📋 Project update:', update)
    // Handle project status update
  }

  const handleMilestone = (milestone: any) => {
    console.log('🎯 Milestone completed:', milestone)
    // Handle milestone completion
  }

  const handleEnergyData = (data: any) => {
    console.log('⚡ Energy data:', data)
    // Handle real-time energy consumption data
  }

  const handleEnergyAlert = (alert: any) => {
    console.log('⚠️ Energy alert:', alert)
    // Handle energy efficiency alert
  }

  const handleMaintenanceNotice = (notice: any) => {
    console.log('🔧 Maintenance notice:', notice)
    // Handle system maintenance notification
  }

  // Computed properties
  const connectionStatus = computed(() => {
    if (isConnected.value) return 'connected'
    if (isConnecting.value || isGettingTempToken.value) return 'connecting'
    if (isInBackoffPeriod.value) return 'backoff'
    if (connectionError.value) return 'error'
    if (!effectiveToken.value) return 'no-auth'
    return 'disconnected'
  })

  // Computed property for next reconnect countdown
  const reconnectCountdown = computed(() => {
    if (!nextReconnectTime.value) return null
    const remaining = Math.max(0, nextReconnectTime.value - Date.now())
    return Math.ceil(remaining / 1000) // Return seconds
  })

  const unreadNotifications = computed(() => {
    return notifications.value.filter(n => !n.read).length
  })

  // Memory management
  const intervals = new Set<NodeJS.Timeout>()
  const timeouts = new Set<NodeJS.Timeout>()

  // Memory-safe interval creation
  const createInterval = (callback: () => void, delay: number) => {
    const interval = setInterval(callback, delay)
    intervals.add(interval)
    return interval
  }

  // Memory-safe timeout creation
  const createTimeout = (callback: () => void, delay: number) => {
    const timeout = setTimeout(() => {
      callback()
      timeouts.delete(timeout)
    }, delay)
    timeouts.add(timeout)
    return timeout
  }

  // Custom reconnection strategy
  const scheduleReconnect = () => {
    if (isInBackoffPeriod.value || reconnectTimer.value) {
      return // Already scheduled
    }

    // Don't schedule reconnect if we're in error backoff mode
    if (consecutiveConnectionErrors >= MAX_CONSECUTIVE_ERRORS) {
      console.log('🚫 Not scheduling reconnect - in error backoff mode')
      return
    }

    // Don't schedule reconnect when offline
    if (!navigator.onLine) {
      console.log('🌐 Not scheduling reconnect - offline')
      return
    }

    reconnectAttempts.value++

    if (reconnectAttempts.value <= maxReconnectAttempts.value) {
      // Enhanced exponential backoff with rate limiting
      const baseDelay = Math.min(1000 * Math.pow(2, reconnectAttempts.value - 1), 10000) // Up to 10s
      const jitter = Math.random() * 1000 // Add jitter to prevent thundering herd
      const delay = baseDelay + jitter

      console.log(`🔄 Scheduling reconnect attempt ${reconnectAttempts.value}/${maxReconnectAttempts.value} in ${Math.round(delay)}ms`)

      reconnectTimer.value = setTimeout(() => {
        reconnectTimer.value = null
        if (!isConnected.value && navigator.onLine) {
          connect()
        }
      }, delay)
    } else {
      // After 3 attempts, wait longer and increment cycle
      reconnectCycle.value++
      const baseBackoffTime = 60000 * Math.min(reconnectCycle.value, 5) // Cap at 5 minutes
      const jitter = Math.random() * 30000 // Add up to 30s jitter
      const backoffTime = baseBackoffTime + jitter

      console.log(`🕐 Max attempts reached. Waiting ${Math.round(backoffTime / 1000)}s before next cycle (cycle ${reconnectCycle.value})`)

      isInBackoffPeriod.value = true
      nextReconnectTime.value = Date.now() + backoffTime
      reconnectAttempts.value = 0 // Reset for next cycle

      reconnectTimer.value = setTimeout(() => {
        reconnectTimer.value = null
        isInBackoffPeriod.value = false
        nextReconnectTime.value = null

        if (!isConnected.value && navigator.onLine) {
          console.log(`🔄 Starting reconnection cycle ${reconnectCycle.value}`)
          connect()
        }
      }, backoffTime)
    }
  }

  // Reset reconnection state
  const resetReconnectionState = () => {
    reconnectAttempts.value = 0
    reconnectCycle.value = 0
    isInBackoffPeriod.value = false
    nextReconnectTime.value = null

    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
  }

  // Reset error counters when network comes back online
  const resetErrorCounters = () => {
    consecutiveConnectionErrors = 0
    console.log('🔄 Socket.io error counters reset')
  }

  // Listen for online events to reset error counters and attempt reconnection
  if (typeof window !== 'undefined') {
    window.addEventListener('online', () => {
      resetErrorCounters()
      if (!isConnected.value && !isConnecting.value) {
        console.log('🌐 Network back online - attempting Socket.io reconnection')
        createTimeout(() => connect(), 1000) // Small delay to ensure network is stable
      }
    })

    window.addEventListener('offline', () => {
      console.log('🌐 Network offline - Socket.io will pause reconnection attempts')
    })
  }

  // Cleanup all intervals and timeouts
  const cleanupTimers = () => {
    intervals.forEach(interval => clearInterval(interval))
    intervals.clear()
    timeouts.forEach(timeout => clearTimeout(timeout))
    timeouts.clear()

    // Clear reconnect timer
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
  }

  // Lifecycle - only register if inside a component
  const instance = getCurrentInstance()
  if (instance) {
    onMounted(async () => {
      console.log('🔌 Socket.io composable initialized')

      // Always try to connect - will get temp token if needed
      console.log('🚀 Auto-connecting to Socket.io...')
      await connect()

      // Set up periodic token refresh (every 45 minutes) with memory management
      createInterval(async () => {
        if (!token.value) { // Only refresh temp tokens
          console.log('🔄 Periodic temp token refresh check...')
          await refreshTempTokenIfNeeded()
        }
      }, 45 * 60 * 1000) // 45 minutes

      // Set up connection health check (every 5 minutes) with rate limiting
      createInterval(() => {
        if (socket.value && !isConnected.value && !isConnecting.value && navigator.onLine) {
          // Only attempt reconnect if not in error backoff mode
          if (consecutiveConnectionErrors < MAX_CONSECUTIVE_ERRORS) {
            console.log('🔄 Connection health check - attempting reconnect...')
            connect()
          } else {
            console.log('🚫 Connection health check skipped - in error backoff mode')
          }
        }
      }, 5 * 60 * 1000) // 5 minutes
    })

    // Clean up on unmount (register at the same level as onMounted)
    onUnmounted(() => {
      console.log('🧹 Cleaning up Socket.io composable...')
      cleanupTimers()
      disconnect()
    })
  } else {
    // If not in component context, just log
    console.log('🔌 Socket.io composable initialized outside component context')
  }

  return {
    // Connection state
    socket: socket.value,
    isConnected,
    isConnecting,
    connectionError,
    connectionStatus,
    reconnectAttempts,
    reconnectCycle,
    isInBackoffPeriod,
    nextReconnectTime,
    reconnectCountdown,
    maxReconnectAttempts,
    lastPing,

    // Token management
    tempToken,
    tempTokenExpiry,
    isGettingTempToken,
    effectiveToken,
    isTempTokenExpired,

    // Data
    onlineUsers,
    notifications,
    analyticsEvents,
    typingUsers,
    unreadNotifications,

    // Methods
    connect,
    disconnect,
    scheduleReconnect,
    resetReconnectionState,
    getTempToken,
    refreshTempTokenIfNeeded,
    trackPageView,
    trackClick,
    trackFormSubmit,
    sendMessage,
    startTyping,
    stopTyping,
    joinRoom,
    leaveRoom,
    markNotificationAsRead
  }
}
